# Repository Guidelines

## Project Structure & Module Organization
The Expo entry point is `App.tsx`, which wires global providers before delegating navigation to `app/navigation.tsx`. Feature code lives in `app/`, split into `components/`, screen stacks under `screens/`, domain helpers in `services/`, shared TypeScript contracts in `types/`, and utility functions in `utils/`. Static media such as logos or audio clips belong in `assets/`. Keep environment configuration in `.env` and reference variables through `react-native-dotenv` imports.

## Build, Test, and Development Commands
Install dependencies with `npm install`. Run the Metro bundler via `npm run start`, or target platforms explicitly using `npm run ios`, `npm run android`, or `npm run web`. These commands launch Expo DevTools, allowing live reload against the emulator or Expo Go. When debugging native modules, restart the packager after editing `babel.config.js` or native config.

## Coding Style & Naming Conventions
The project uses TypeScript with `strict` mode (see `tsconfig.json`), so prefer explicit types on module boundaries. Follow the prevailing two-space indentation, single quotes, and semicolon usage seen in `App.tsx`. Components and screens use PascalCase filenames (e.g., `ChatScreen.tsx`), while hooks or utilities stay camelCase. Co-locate styles with their components and favour functional components with React hooks. Re-export shared interfaces from `app/types` to keep imports tidy.

## Testing Guidelines
Automated tests are not yet configured; when introducing Jest or Expo Testing Library, place specs alongside code in `__tests__/` folders and add an `npm test` script. Until then, document manual verification steps in pull requests and validate core flows in Expo Go on both iOS and Android where possible. Aim for high-coverage tests around services that call the Mastra client once tooling is added.

## Commit & Pull Request Guidelines
Write commits in the imperative mood (e.g., `Implement chat retry logic`), mirroring the existing history. Group related changes and avoid bundling dependency bumps with feature work. Pull requests should include: a concise summary, linked Mastra issue or task ID, screenshots or screen recordings for UI updates, and a checklist of manual tests performed. Note any configuration or schema changes so reviewers can follow along.

## Security & Configuration Tips
Never commit secrets or production URLs—`.env` is gitignored by default. Document new environment flags in the README and provide safe defaults where possible. When adding native modules or permissions, update `app.json` and call out the changes during review to trigger the appropriate platform re-signing.
