# Mastra Mobile App

A React Native mobile app that connects to the Mastra AI agent, now featuring an additional AskARA CRM interface for cleaning service management.

## Features

### Chat Interface
- Connect to Mastra AI agents
- Real-time streaming responses
- Connection status monitoring
- Configurable Mastra Cloud URL and Agent ID

### AskARA CRM Interface
- **Dashboard View**: Job progress tracking with circular progress indicators
- **Task Management**: Interactive task lists with expandable checklist items
- **Schedule Management**: Upcoming shift scheduling and calendar integration
- **Navigation**: Bottom tab navigation between Home, Tasks, and Schedule sections
- **Modern UI**: Native mobile interface adapted from the original Next.js design

## Prerequisites

- Node.js (v18 or newer)
- npm
- Expo Go app on your mobile device

## Running the App Locally

### 1. Clone the Repository
```bash
git clone https://github.com/OmkarBansod02/mastra-expo-app.git
cd mastra-expo-app
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Configuration

You'll need to configure your Mastra Cloud URL and Agent ID in the app settings when you first launch it.

### 4. Start the App
```bash
npx expo start
```

### 5. Run on Your Device
- Install Expo Go app on your iOS or Android device
- Scan the QR code from the terminal with your camera app
- The app will open in Expo Go

## Navigation

The app includes two main interfaces:

1. **Chat Screen** (Default): Mastra AI agent interface
2. **AskARA Screen**: CRM interface for cleaning service management

Navigate between screens using the icons in the header bar.