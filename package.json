{"name": "mast<PERSON>ersonalassistant", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@mastra/client-js": "^0.1.22", "@react-native-async-storage/async-storage": "2.2.0", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.26", "expo": "^54.0.0", "expo-status-bar": "~3.0.8", "react": "19.1.0", "react-native": "0.81.4", "react-native-dotenv": "^3.4.11", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0"}, "devDependencies": {"@babel/core": "^7.28.4", "@types/react": "~19.1.10", "typescript": "^5.9.2"}, "private": true, "trustedDependencies": ["onnxruntime-node", "protobufjs"]}