import React from 'react';
import { View, ViewProps, StyleSheet, TouchableOpacity, TouchableOpacityProps } from 'react-native';
import { Text, TextProps } from 'react-native-paper';
import { designTokens, cn } from '../../utils/designSystem';

// Card component matching Radix UI Card
interface CardProps extends ViewProps {
  variant?: 'default' | 'outlined' | 'elevated';
}

export const Card: React.FC<CardProps> = ({ variant = 'default', style, children, ...props }) => {
  const cardStyles = [
    styles.cardBase,
    variant === 'outlined' && styles.cardOutlined,
    variant === 'elevated' && styles.cardElevated,
    style,
  ];

  return (
    <View style={cardStyles} {...props}>
      {children}
    </View>
  );
};

// CardHeader component
interface CardHeaderProps extends ViewProps {
  spacing?: 'compact' | 'normal' | 'loose';
}

export const CardHeader: React.FC<CardHeaderProps> = ({ spacing = 'normal', style, children, ...props }) => {
  const headerStyles = [
    styles.cardHeader,
    spacing === 'compact' && styles.cardHeaderCompact,
    spacing === 'loose' && styles.cardHeaderLoose,
    style,
  ];

  return (
    <View style={headerStyles} {...props}>
      {children}
    </View>
  );
};

// CardTitle component
interface CardTitleProps extends TextProps<string> {
  size?: 'sm' | 'md' | 'lg';
  weight?: 'normal' | 'semibold' | 'bold';
}

export const CardTitle: React.FC<CardTitleProps> = ({ 
  size = 'md', 
  weight = 'semibold',
  style, 
  children, 
  ...props 
}) => {
  const titleStyles = [
    styles.cardTitle,
    size === 'sm' && styles.cardTitleSm,
    size === 'lg' && styles.cardTitleLg,
    weight === 'normal' && styles.cardTitleNormal,
    weight === 'bold' && styles.cardTitleBold,
    style,
  ];

  return (
    <Text style={titleStyles} {...props}>
      {children}
    </Text>
  );
};

// CardDescription component
export const CardDescription: React.FC<TextProps<string>> = ({ style, children, ...props }) => (
  <Text style={[styles.cardDescription, style]} {...props}>
    {children}
  </Text>
);

// CardContent component
export const CardContent: React.FC<ViewProps> = ({ style, children, ...props }) => (
  <View style={[styles.cardContent, style]} {...props}>
    {children}
  </View>
);

// CardFooter component
export const CardFooter: React.FC<ViewProps> = ({ style, children, ...props }) => (
  <View style={[styles.cardFooter, style]} {...props}>
    {children}
  </View>
);

// Badge component matching Radix UI Badge
interface BadgeProps extends ViewProps {
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning';
  size?: 'sm' | 'md';
}

export const Badge: React.FC<BadgeProps> = ({ 
  variant = 'default', 
  size = 'md',
  style, 
  children, 
  ...props 
}) => {
  const badgeStyles = [
    styles.badgeBase,
    styles[`badge${variant.charAt(0).toUpperCase() + variant.slice(1)}` as keyof typeof styles],
    size === 'sm' && styles.badgeSm,
    style,
  ];

  return (
    <View style={badgeStyles} {...props}>
      <Text style={[
        styles.badgeText,
        styles[`badgeText${variant.charAt(0).toUpperCase() + variant.slice(1)}` as keyof typeof styles],
        size === 'sm' && styles.badgeTextSm,
      ]}>
        {children}
      </Text>
    </View>
  );
};

// Button component matching Radix UI Button
interface ButtonProps extends TouchableOpacityProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({ 
  variant = 'default', 
  size = 'default',
  style,
  children,
  ...props 
}) => {
  const buttonStyles = [
    styles.buttonBase,
    styles[`button${variant.charAt(0).toUpperCase() + variant.slice(1)}` as keyof typeof styles],
    styles[`button${size.charAt(0).toUpperCase() + size.slice(1)}` as keyof typeof styles],
    style,
  ];

  return (
    <TouchableOpacity style={buttonStyles} {...props}>
      <Text style={[
        styles.buttonText,
        styles[`buttonText${variant.charAt(0).toUpperCase() + variant.slice(1)}` as keyof typeof styles],
        styles[`buttonText${size.charAt(0).toUpperCase() + size.slice(1)}` as keyof typeof styles],
      ]}>
        {children}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Card styles
  cardBase: {
    borderRadius: designTokens.radius.lg,
    backgroundColor: designTokens.colors.card,
    borderWidth: 1,
    borderColor: designTokens.colors.border,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  cardOutlined: {
    shadowOpacity: 0,
    elevation: 0,
  },
  cardElevated: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  cardHeader: {
    padding: designTokens.spacing[6],
    paddingBottom: designTokens.spacing[1.5],
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  cardHeaderCompact: {
    padding: designTokens.spacing[4],
    paddingBottom: designTokens.spacing[1],
  },
  cardHeaderLoose: {
    padding: designTokens.spacing[8],
    paddingBottom: designTokens.spacing[2],
  },
  cardTitle: {
    fontSize: designTokens.typography.fontSize.lg,
    fontWeight: designTokens.typography.fontWeight.semibold,
    lineHeight: designTokens.typography.lineHeight.tight,
    color: designTokens.colors.cardForeground,
    letterSpacing: -0.025,
  },
  cardTitleSm: {
    fontSize: designTokens.typography.fontSize.base,
  },
  cardTitleLg: {
    fontSize: designTokens.typography.fontSize['2xl'],
  },
  cardTitleNormal: {
    fontWeight: designTokens.typography.fontWeight.normal,
  },
  cardTitleBold: {
    fontWeight: designTokens.typography.fontWeight.bold,
  },
  cardDescription: {
    fontSize: designTokens.typography.fontSize.sm,
    color: designTokens.colors.muted.foreground,
    lineHeight: designTokens.typography.lineHeight.normal,
  },
  cardContent: {
    paddingHorizontal: designTokens.spacing[6],
    paddingBottom: designTokens.spacing[6],
  },
  cardFooter: {
    padding: designTokens.spacing[6],
    paddingTop: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },

  // Badge styles
  badgeBase: {
    display: 'inline-flex',
    alignItems: 'center',
    borderRadius: 9999,
    borderWidth: 1,
    paddingHorizontal: designTokens.spacing[2.5],
    paddingVertical: designTokens.spacing[0.5],
  },
  badgeSm: {
    paddingHorizontal: designTokens.spacing[2],
    paddingVertical: designTokens.spacing[0.25],
  },
  badgeDefault: {
    backgroundColor: designTokens.colors.primary.DEFAULT,
    borderColor: 'transparent',
  },
  badgeSecondary: {
    backgroundColor: designTokens.colors.secondary.DEFAULT,
    borderColor: 'transparent',
  },
  badgeDestructive: {
    backgroundColor: designTokens.colors.destructive.DEFAULT,
    borderColor: 'transparent',
  },
  badgeOutline: {
    backgroundColor: 'transparent',
    borderColor: designTokens.colors.foreground,
  },
  badgeSuccess: {
    backgroundColor: designTokens.colors.green[500],
    borderColor: 'transparent',
  },
  badgeWarning: {
    backgroundColor: designTokens.colors.yellow[500],
    borderColor: 'transparent',
  },
  badgeText: {
    fontSize: designTokens.typography.fontSize.xs,
    fontWeight: designTokens.typography.fontWeight.semibold,
    textAlign: 'center',
  },
  badgeTextSm: {
    fontSize: 10,
  },
  badgeTextDefault: {
    color: designTokens.colors.primary.foreground,
  },
  badgeTextSecondary: {
    color: designTokens.colors.secondary.foreground,
  },
  badgeTextDestructive: {
    color: designTokens.colors.destructive.foreground,
  },
  badgeTextOutline: {
    color: designTokens.colors.foreground,
  },
  badgeTextSuccess: {
    color: '#fff',
  },
  badgeTextWarning: {
    color: '#fff',
  },

  // Button styles
  buttonBase: {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: designTokens.radius.md,
    fontSize: designTokens.typography.fontSize.sm,
    fontWeight: designTokens.typography.fontWeight.medium,
    lineHeight: designTokens.typography.lineHeight.tight,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  buttonDefault: {
    backgroundColor: designTokens.colors.primary.DEFAULT,
    borderColor: designTokens.colors.primary.DEFAULT,
  },
  buttonDestructive: {
    backgroundColor: designTokens.colors.destructive.DEFAULT,
    borderColor: designTokens.colors.destructive.DEFAULT,
  },
  buttonOutline: {
    backgroundColor: 'transparent',
    borderColor: designTokens.colors.border,
  },
  buttonSecondary: {
    backgroundColor: designTokens.colors.secondary.DEFAULT,
    borderColor: designTokens.colors.secondary.DEFAULT,
  },
  buttonGhost: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
  },
  buttonLink: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
  },
  buttonDefaultSize: {
    height: 40,
    paddingHorizontal: designTokens.spacing[4],
    paddingVertical: designTokens.spacing[2],
  },
  buttonSm: {
    height: 36,
    paddingHorizontal: designTokens.spacing[3],
    paddingVertical: designTokens.spacing[1.5],
    fontSize: designTokens.typography.fontSize.xs,
  },
  buttonLg: {
    height: 44,
    paddingHorizontal: designTokens.spacing[8],
    paddingVertical: designTokens.spacing[2],
    fontSize: designTokens.typography.fontSize.base,
  },
  buttonIcon: {
    height: 40,
    width: 40,
    padding: designTokens.spacing[2],
  },
  buttonText: {
    textAlign: 'center',
  },
  buttonTextDefault: {
    color: designTokens.colors.primary.foreground,
  },
  buttonTextDestructive: {
    color: designTokens.colors.destructive.foreground,
  },
  buttonTextOutline: {
    color: designTokens.colors.foreground,
  },
  buttonTextSecondary: {
    color: designTokens.colors.secondary.foreground,
  },
  buttonTextGhost: {
    color: designTokens.colors.foreground,
  },
  buttonTextLink: {
    color: designTokens.colors.primary.DEFAULT,
    textDecorationLine: 'underline',
  },
});

export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter, Badge, Button };