import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  Text,
  Dimensions
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardHeader, CardTitle, CardContent, CardFooter, Badge, Button } from '../components/ui';
import { designTokens } from '../utils/designSystem';

const { width: screenWidth } = Dimensions.get('window');

// Mock data matching the original ask-ara component
const currentJob = {
  title: "Office Building A - Weekly Cleaning",
  client: "TechCorp Inc.",
  progress: 65,
  tasks: {
    completed: 13,
    inProgress: 5,
    remaining: 7,
  },
  timeRemaining: "2 hours 15 minutes",
  rooms: {
    cleaned: ["Lobby", "Conference Room A", "Offices 101-105", "Restrooms 1F"],
    remaining: ["Offices 106-110", "Break Room", "Restrooms 2F", "Storage Area"],
  },
};

const tasks = [
  {
    id: "task1",
    title: "Office Building A",
    status: "In Progress",
    checklist: [
      { id: "check1", task: "Vacuum all carpets", completed: false },
      { id: "check2", task: "Clean and sanitize bathrooms", completed: true },
      { id: "check3", task: "Empty trash bins", completed: false },
      { id: "check4", task: "Dust all surfaces", completed: false },
      { id: "check5", task: "Mop hard floors", completed: true },
    ],
  },
  {
    id: "task2",
    title: "Retail Store B",
    status: "Pending",
    checklist: [
      { id: "check6", task: "Clean display windows", completed: false },
      { id: "check7", task: "Sanitize checkout counters", completed: false },
      { id: "check8", task: "Restock cleaning supplies", completed: true },
      { id: "check9", task: "Clean fitting rooms", completed: false },
    ],
  },
  {
    id: "task3",
    title: "Medical Clinic C",
    status: "Completed",
    checklist: [
      { id: "check10", task: "Disinfect waiting area", completed: true },
      { id: "check11", task: "Sanitize examination rooms", completed: true },
      { id: "check12", task: "Clean and organize reception", completed: true },
      { id: "check13", task: "Dispose of medical waste", completed: true },
    ],
  },
];

const schedules = [
  {
    id: "schedule1",
    date: "2023-10-16",
    shift: "Morning Shift",
    time: "6:00 AM - 2:00 PM",
    location: "Office Building A",
  },
  {
    id: "schedule2",
    date: "2023-10-17",
    shift: "Afternoon Shift",
    time: "2:00 PM - 10:00 PM",
    location: "Retail Store B",
  },
  {
    id: "schedule3",
    date: "2023-10-18",
    shift: "Morning Shift",
    time: "6:00 AM - 2:00 PM",
    location: "Medical Clinic C",
  },
  {
    id: "schedule4",
    date: "2023-10-19",
    shift: "Night Shift",
    time: "10:00 PM - 6:00 AM",
    location: "Office Building D",
  },
];

const contacts = [
  { name: "John Doe", jobType: "Cleaner", avatar: "JD" },
  { name: "Jane Smith", jobType: "Supervisor", avatar: "JS" },
  { name: "Mike Johnson", jobType: "Manager", avatar: "MJ" },
];

const team = [
  {
    name: "Sarah Manager",
    role: "Supervisor",
    avatar: "https://i.pravatar.cc/150?img=1",
    phone: "+**********",
    email: "<EMAIL>",
  },
  {
    name: "John Cleaner",
    role: "Senior Cleaner",
    avatar: "https://i.pravatar.cc/150?img=2",
    phone: "+**********",
    email: "<EMAIL>",
  },
  {
    name: "Emily Tidy",
    role: "Cleaner",
    avatar: "https://i.pravatar.cc/150?img=3",
    phone: "+**********",
    email: "<EMAIL>",
  },
  {
    name: "Michael Sweep",
    role: "Cleaner",
    avatar: "https://i.pravatar.cc/150?img=4",
    phone: "+**********",
    email: "<EMAIL>",
  },
];

export type RootStackParamList = {
  Chat: undefined;
  Settings: undefined;
  AskARA: undefined;
};

const AskARAScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState("home");
  const [expandedTask, setExpandedTask] = useState<string | null>(null);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const navigation = useNavigation();

  const toggleTaskExpansion = (taskId: string) => {
    setExpandedTask(expandedTask === taskId ? null : taskId);
  };

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'success';
      case 'In Progress':
        return 'warning';
      case 'Pending':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const CircularProgress = ({ value, size = 80 }: { value: number; size?: number }) => (
    <View style={[styles.circularProgress, { width: size, height: size, borderRadius: size / 2 }]} >
      <View style={styles.circularProgressBackground} />
      <View style={[styles.circularProgressFill, { 
        width: size * 0.8, 
        height: size * 0.8, 
        borderRadius: (size * 0.8) / 2,
        backgroundColor: `rgba(74, 222, 128, ${value / 100})`,
      }]} />
      <Text style={[styles.circularProgressText, { fontSize: size * 0.2 }]} >{value}%</Text>
    </View>
  );

  const renderDashboard = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Current Job Card - Black background with green progress */}
      <Card variant="elevated" style={styles.jobCard}>
        <CardHeader spacing="normal">
          <View style={styles.cardHeaderRow}>
            <CardTitle size="md" weight="semibold" style={styles.jobTitle}>{currentJob.title}</CardTitle>
            <Ionicons name="sparkles" size={16} color="#fbbf24" />
          </View>
        </CardHeader>
        
        <CardContent>
          <View style={styles.progressSection}>
            <CircularProgress value={currentJob.progress} size={80} />
            
            <View style={styles.jobInfo}>
              <Text style={styles.clientText}>Client: {currentJob.client}</Text>
              <Text style={styles.timeText}>Time Remaining: {currentJob.timeRemaining}</Text>
            </View>
          </View>

          <View style={styles.tasksGrid}>
            <View style={styles.taskColumn}>
              <Text style={styles.columnTitle}>Completed Tasks</Text>
              {currentJob.rooms.cleaned.slice(0, 3).map((room, index) => (
                <View key={index} style={styles.taskItem}>
                  <Ionicons name="checkmark-circle" size={14} color="#4ade80" />
                  <Text style={styles.taskText}>{room}</Text>
                </View>
              ))}
            </View>
            
            <View style={styles.taskColumn}>
              <Text style={styles.columnTitle}>Remaining Tasks</Text>
              {currentJob.rooms.remaining.slice(0, 3).map((room, index) => (
                <View key={index} style={styles.taskItem}>
                  <Ionicons name="square-outline" size={14} color="#9ca3af" />
                  <Text style={styles.taskText}>{room}</Text>
                </View>
              ))}
            </View>
          </View>
        </CardContent>
      </Card>

      {/* Tasks Breakdown */}
      <Card style={styles.sectionCard}>
        <CardHeader spacing="compact">
          <CardTitle size="sm" weight="medium" style={styles.sectionTitle}>TASKS BREAKDOWN</CardTitle>
        </CardHeader>
        
        <CardContent>
          {tasks.map((task) => (
            <View key={task.id} style={styles.taskCard}>
              <TouchableOpacity
                style={styles.taskHeader}
                onPress={() => toggleTaskExpansion(task.id)}
              >
                <Text style={styles.taskTitle}>{task.title}</Text>
                <View style={styles.taskStatusContainer}>
                  <Badge variant={getStatusBadgeVariant(task.status) as any} size="sm">
                    {task.status}
                  </Badge>
                  <Ionicons
                    name={expandedTask === task.id ? "chevron-up" : "chevron-down"}
                    size={16}
                    color="#6b7280"
                  />
                </View>
              </TouchableOpacity>
              
              {expandedTask === task.id && (
                <View style={styles.taskChecklist}>
                  {task.checklist.map((item) => (
                    <View key={item.id} style={styles.checklistItem}>
                      {item.completed ? (
                        <Ionicons name="checkmark-circle" size={14} color="#4ade80" />
                      ) : (
                        <Ionicons name="square-outline" size={16} color="#d1d5db" />
                      )}
                      <Text
                        style={[
                          styles.checklistText,
                          item.completed && styles.checklistTextCompleted,
                        ]}
                      >
                        {item.task}
                      </Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          ))}
        </CardContent>
      </Card>

      {/* Upcoming Shifts */}
      <Card style={styles.sectionCard}>
        <CardHeader spacing="compact">
          <CardTitle size="sm" weight="medium" style={styles.sectionTitle}>UPCOMING SHIFTS</CardTitle>
        </CardHeader>
        
        <CardContent>
          {schedules.slice(0, 3).map((schedule) => (
            <Card key={schedule.id} style={styles.scheduleCard}>
              <CardHeader spacing="compact">
                <CardTitle size="sm" weight="semibold">
                  {new Date(schedule.date).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Text style={styles.scheduleShift}>{schedule.shift}</Text>
                <Text style={styles.scheduleTime}>{schedule.time}</Text>
                <Text style={styles.scheduleLocation}>Location: {schedule.location}</Text>
              </CardContent>
            </Card>
          ))}
        </CardContent>
      </Card>

      {/* ARA GROUP CONTACTS */}
      <Card style={styles.sectionCard}>
        <CardHeader spacing="compact">
          <CardTitle size="sm" weight="medium" style={styles.sectionTitle}>ARA GROUP CONTACTS</CardTitle>
        </CardHeader>
        
        <CardContent>
          {contacts.map((contact, index) => (
            <View key={index} style={styles.contactItem}>
              <View style={styles.contactAvatar}>
                <Text style={styles.contactAvatarText}>{contact.avatar}</Text>
              </View>
              <View style={styles.contactInfo}>
                <Text style={styles.contactName}>{contact.name}</Text>
                <Text style={styles.contactRole}>{contact.jobType}</Text>
              </View>
            </View>
          ))}
        </CardContent>
      </Card>
    </ScrollView>
  );

  const renderTasks = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <Text style={styles.sectionHeader}>CLEANING TASKS</Text>
      {tasks.map((task) => (
        <Card key={task.id} style={styles.taskCard}>
          <CardHeader spacing="compact">
            <View style={styles.taskHeaderRow}
>
              <CardTitle size="sm" weight="semibold" style={styles.taskCardTitle}>{task.title}</CardTitle>
              <Badge variant={getStatusBadgeVariant(task.status) as any} size="sm">
                {task.status}
              </Badge>
            </View>
          </CardHeader>
          
          <CardContent>
            <View style={styles.taskChecklist}>
              {task.checklist.map((item) => (
                <View key={item.id} style={styles.checklistItem}>
                  {item.completed ? (
                    <Ionicons name="checkmark-circle" size={14} color="#4ade80" />
                  ) : (
                    <Ionicons name="square-outline" size={16} color="#d1d5db" />
                  )}
                  <Text
                    style={[
                      styles.checklistText,
                      item.completed && styles.checklistTextCompleted,
                    ]}
                  >
                    {item.task}
                  </Text>
                </View>
              ))}
            </View>
          </CardContent>
        </Card>
      ))}
    </ScrollView>
  );

  const renderSchedule = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <Text style={styles.sectionHeader}>UPCOMING SHIFTS</Text>
      
      <View style={styles.scheduleActions}>
        <Button variant="outline" size="sm" onPress={() => {}}>
          <Ionicons name="calendar" size={16} color={designTokens.colors.primary.DEFAULT} />
          View Calendar
        </Button>
        <Button variant="outline" size="sm" onPress={() => {}}>
          <Ionicons name="time" size={16} color={designTokens.colors.primary.DEFAULT} />
          Request Leave
        </Button>
      </View>
      
      {schedules.map((schedule) => (
        <Card key={schedule.id} style={styles.scheduleCard}>
          <CardHeader spacing="compact">
            <CardTitle size="sm" weight="semibold">
              {new Date(schedule.date).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Text style={styles.scheduleShift}>{schedule.shift}</Text>
            <Text style={styles.scheduleTime}>{schedule.time}</Text>
            <Text style={styles.scheduleLocation}>Location: {schedule.location}</Text>
          </CardContent>
        </Card>
      ))}
    </ScrollView>
  );

  const renderTeam = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <Text style={styles.sectionHeader}>CLEANING TEAM</Text>
      {team.map((member, index) => (
        <Card key={index} style={styles.teamCard}>
          <CardContent>
            <View style={styles.teamMemberRow}>
              <View style={styles.teamAvatar}>
                <Text style={styles.teamAvatarText}>
                  {member.name.split(' ').map((n) => n[0]).join('')}
                </Text>
              </View>
              <View style={styles.teamInfo}>
                <Text style={styles.teamMemberName}>{member.name}</Text>
                <Text style={styles.teamMemberRole}>{member.role}</Text>
              </View>
              
              <View style={styles.teamActions}>
                <Button variant="outline" size="icon" onPress={() => {}}>
                  <Ionicons name="call" size={16} color={designTokens.colors.primary.DEFAULT} />
                </Button>
                <Button variant="outline" size="icon" onPress={() => {}}>
                  <Ionicons name="chatbubbles" size={16} color={designTokens.colors.primary.DEFAULT} />
                </Button>
              </View>
            </View>
          </CardContent>
        </Card>
      ))}
    </ScrollView>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'home':
        return renderDashboard();
      case 'tasks':
        return renderTasks();
      case 'schedule':
        return renderSchedule();
      case 'team':
        return renderTeam();
      default:
        return renderDashboard();
    }
  };

  return (
    <SafeAreaView style={[styles.safeArea, isDarkMode && styles.safeAreaDark]}>
      <StatusBar 
        backgroundColor={isDarkMode ? designTokens.colors.dark.background : designTokens.colors.background} 
        barStyle={isDarkMode ? "light-content" : "dark-content"} 
      />
      
      <View style={[styles.container, isDarkMode && styles.containerDark]}>
        {/* Header */}
        <View style={[styles.header, isDarkMode && styles.headerDark]}>
          <Text style={[styles.headerTitle, isDarkMode && styles.headerTitleDark]}>ARA Group CRM</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity onPress={toggleTheme} style={styles.headerButton}>
              <Ionicons 
                name={isDarkMode ? "sunny" : "moon"} 
                size={20} 
                color={isDarkMode ? designTokens.colors.dark.foreground : designTokens.colors.foreground} 
              />
            </TouchableOpacity>
            
            <TouchableOpacity 
              onPress={() => navigation.navigate('Settings' as never)} 
              style={styles.headerButton}
            >
              <Ionicons 
                name="settings" 
                size={20} 
                color={isDarkMode ? designTokens.colors.dark.foreground : designTokens.colors.foreground} 
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Tab Content */}
        <View style={styles.content}>
          {renderTabContent()}
        </View>

        {/* Bottom Navigation */}
        <View style={[styles.bottomNav, isDarkMode && styles.bottomNavDark]}>
          <TouchableOpacity
            style={[styles.navButton, activeTab === 'home' && styles.navButtonActive]}
            onPress={() => setActiveTab('home')}
          >
            <Ionicons
              name="home"
              size={24}
              color={activeTab === 'home' ? designTokens.colors.blue[500] : designTokens.colors.gray[500]}
            />
            <Text style={[styles.navLabel, activeTab === 'home' && styles.navLabelActive]}>
              Home
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, activeTab === 'tasks' && styles.navButtonActive]}
            onPress={() => setActiveTab('tasks')}
          >
            <Ionicons
              name="clipboard"
              size={24}
              color={activeTab === 'tasks' ? designTokens.colors.yellow[500] : designTokens.colors.gray[500]}
            />
            <Text style={[styles.navLabel, activeTab === 'tasks' && styles.navLabelActive]}>
              Tasks
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, activeTab === 'schedule' && styles.navButtonActive]}
            onPress={() => setActiveTab('schedule')}
          >
            <Ionicons
              name="calendar"
              size={24}
              color={activeTab === 'schedule' ? designTokens.colors.green[500] : designTokens.colors.gray[500]}
            />
            <Text style={[styles.navLabel, activeTab === 'schedule' && styles.navLabelActive]}>
              Schedule
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, activeTab === 'team' && styles.navButtonActive]}
            onPress={() => setActiveTab('team')}
          >
            <Ionicons
              name="people"
              size={24}
              color={activeTab === 'team' ? designTokens.colors.purple[500] : designTokens.colors.gray[500]}
            />
            <Text style={[styles.navLabel, activeTab === 'team' && styles.navLabelActive]}>
              Team
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  // Layout
  safeArea: {
    flex: 1,
    backgroundColor: designTokens.colors.background,
  },
  safeAreaDark: {
    backgroundColor: designTokens.colors.dark.background,
  },
  container: {
    flex: 1,
    backgroundColor: designTokens.colors.background,
  },
  containerDark: {
    backgroundColor: designTokens.colors.dark.background,
  },
  content: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
    padding: designTokens.spacing[4],
  },

  // Header
  header: {
    backgroundColor: designTokens.colors.background,
    paddingHorizontal: designTokens.spacing[4],
    paddingVertical: designTokens.spacing[4],
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: designTokens.colors.border,
  },
  headerDark: {
    backgroundColor: designTokens.colors.dark.background,
    borderBottomColor: designTokens.colors.dark.border,
  },
  headerTitle: {
    fontSize: designTokens.typography.fontSize.xl,
    fontWeight: designTokens.typography.fontWeight.bold,
    color: designTokens.colors.foreground,
    letterSpacing: -0.025,
  },
  headerTitleDark: {
    color: designTokens.colors.dark.foreground,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing[2],
  },
  headerButton: {
    padding: designTokens.spacing[2],
    borderRadius: designTokens.radius.md,
  },

  // Navigation
  bottomNav: {
    backgroundColor: designTokens.colors.background,
    borderTopWidth: 1,
    borderTopColor: designTokens.colors.border,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: designTokens.spacing[3],
    paddingHorizontal: designTokens.spacing[2],
  },
  bottomNavDark: {
    backgroundColor: designTokens.colors.dark.background,
    borderTopColor: designTokens.colors.dark.border,
  },
  navButton: {
    alignItems: 'center',
    paddingVertical: designTokens.spacing[2],
    paddingHorizontal: designTokens.spacing[3],
    borderRadius: designTokens.radius.lg,
    minWidth: 60,
  },
  navButtonActive: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
  },
  navLabel: {
    fontSize: designTokens.typography.fontSize.xs,
    color: designTokens.colors.gray[500],
    marginTop: designTokens.spacing[1],
    fontWeight: designTokens.typography.fontWeight.medium,
  },
  navLabelActive: {
    color: designTokens.colors.blue[500],
    fontWeight: designTokens.typography.fontWeight.semibold,
  },

  // Dashboard
  jobCard: {
    backgroundColor: '#000',
    marginBottom: designTokens.spacing[4],
    borderRadius: designTokens.radius.lg,
    borderWidth: 1,
    borderColor: '#333',
  },
  cardHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  jobTitle: {
    color: '#fff',
    fontSize: designTokens.typography.fontSize.lg,
    fontWeight: designTokens.typography.fontWeight.semibold,
    flex: 1,
  },
  progressSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: designTokens.spacing[4],
  },
  circularProgress: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: designTokens.spacing[4],
    position: 'relative',
  },
  circularProgressBackground: {
    position: 'absolute',
    backgroundColor: '#374151',
    borderRadius: 9999,
  },
  circularProgressFill: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  circularProgressText: {
    color: '#fff',
    fontWeight: designTokens.typography.fontWeight.bold,
    textAlign: 'center',
  },
  jobInfo: {
    flex: 1,
  },
  clientText: {
    color: '#9ca3af',
    fontSize: designTokens.typography.fontSize.sm,
    marginBottom: designTokens.spacing[1],
  },
  timeText: {
    color: '#fff',
    fontSize: designTokens.typography.fontSize.sm,
  },
  tasksGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: designTokens.spacing[4],
  },
  taskColumn: {
    flex: 1,
  },
  columnTitle: {
    color: '#fff',
    fontSize: designTokens.typography.fontSize.xs,
    fontWeight: designTokens.typography.fontWeight.semibold,
    marginBottom: designTokens.spacing[2],
    textTransform: 'uppercase',
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: designTokens.spacing[1],
    gap: designTokens.spacing[1],
  },
  taskText: {
    color: '#fff',
    fontSize: designTokens.typography.fontSize.xs,
  },

  // Section Cards
  sectionCard: {
    marginBottom: designTokens.spacing[4],
    borderRadius: designTokens.radius.lg,
  },
  sectionTitle: {
    fontSize: designTokens.typography.fontSize.xs,
    fontWeight: designTokens.typography.fontWeight.semibold,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    color: designTokens.colors.gray[500],
  },

  // Task Cards
  taskCard: {
    marginBottom: designTokens.spacing[3],
    borderRadius: designTokens.radius.md,
    borderWidth: 1,
    borderColor: designTokens.colors.border,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: designTokens.spacing[4],
    paddingVertical: designTokens.spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: designTokens.colors.border,
  },
  taskHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  taskCardTitle: {
    flex: 1,
    fontSize: designTokens.typography.fontSize.sm,
  },
  taskTitle: {
    fontSize: designTokens.typography.fontSize.sm,
    fontWeight: designTokens.typography.fontWeight.medium,
    color: designTokens.colors.foreground,
    flex: 1,
  },
  taskStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing[2],
  },
  taskChecklist: {
    paddingTop: designTokens.spacing[3],
  },
  checklistItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: designTokens.spacing[2],
    gap: designTokens.spacing[2],
  },
  checklistText: {
    fontSize: designTokens.typography.fontSize.sm,
    color: designTokens.colors.foreground,
    flex: 1,
  },
  checklistTextCompleted: {
    textDecorationLine: 'line-through',
    color: designTokens.colors.gray[500],
  },

  // Schedule
  sectionHeader: {
    fontSize: designTokens.typography.fontSize.sm,
    fontWeight: designTokens.typography.fontWeight.semibold,
    textTransform: 'uppercase',
    marginBottom: designTokens.spacing[4],
    color: designTokens.colors.gray[500],
    letterSpacing: 0.5,
  },
  scheduleActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: designTokens.spacing[4],
    gap: designTokens.spacing[2],
  },
  scheduleCard: {
    marginBottom: designTokens.spacing[3],
    borderRadius: designTokens.radius.md,
    borderWidth: 1,
    borderColor: designTokens.colors.border,
  },
  scheduleShift: {
    fontSize: designTokens.typography.fontSize.sm,
    fontWeight: designTokens.typography.fontWeight.semibold,
    marginBottom: designTokens.spacing[0.5],
  },
  scheduleTime: {
    fontSize: designTokens.typography.fontSize.xs,
    color: designTokens.colors.gray[500],
    marginBottom: designTokens.spacing[0.5],
  },
  scheduleLocation: {
    fontSize: designTokens.typography.fontSize.xs,
    color: designTokens.colors.gray[500],
  },

  // Team
  teamCard: {
    marginBottom: designTokens.spacing[3],
    borderRadius: designTokens.radius.md,
    borderWidth: 1,
    borderColor: designTokens.colors.border,
  },
  teamMemberRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  teamAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: designTokens.colors.primary.DEFAULT,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: designTokens.spacing[3],
  },
  teamAvatarText: {
    color: designTokens.colors.primary.foreground,
    fontSize: designTokens.typography.fontSize.sm,
    fontWeight: designTokens.typography.fontWeight.semibold,
  },
  teamInfo: {
    flex: 1,
  },
  teamMemberName: {
    fontSize: designTokens.typography.fontSize.sm,
    fontWeight: designTokens.typography.fontWeight.semibold,
    color: designTokens.colors.foreground,
  },
  teamMemberRole: {
    fontSize: designTokens.typography.fontSize.xs,
    color: designTokens.colors.gray[500],
  },
  teamActions: {
    flexDirection: 'row',
    gap: designTokens.spacing[2],
  },

  // Contacts
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: designTokens.spacing[3],
  },
  contactAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: designTokens.colors.primary.DEFAULT,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: designTokens.spacing[3],
  },
  contactAvatarText: {
    color: designTokens.colors.primary.foreground,
    fontSize: designTokens.typography.fontSize.xs,
    fontWeight: designTokens.typography.fontWeight.semibold,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: designTokens.typography.fontSize.sm,
    fontWeight: designTokens.typography.fontWeight.medium,
    color: designTokens.colors.foreground,
  },
  contactRole: {
    fontSize: designTokens.typography.fontSize.xs,
    color: designTokens.colors.gray[500],
  },
});

export default AskARAScreen;