import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, SafeAreaView, StatusBar, TouchableOpacity, Text } from 'react-native';
import { Card, Title, Paragraph, Avatar, Button as <PERSON>Button, Badge } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import theme from '../utils/theme';

// Mock data similar to the original ask-ara component
const currentJob = {
  title: "Office Building A - Weekly Cleaning",
  client: "TechCorp Inc.",
  progress: 65,
  tasks: {
    completed: 13,
    inProgress: 5,
    remaining: 7,
  },
  timeRemaining: "2 hours 15 minutes",
  rooms: {
    cleaned: ["Lobby", "Conference Room A", "Offices 101-105", "Restrooms 1F"],
    remaining: ["Offices 106-110", "Break Room", "Restrooms 2F", "Storage Area"],
  },
};

const tasks = [
  {
    id: "task1",
    title: "Office Building A",
    status: "In Progress",
    checklist: [
      { id: "check1", task: "Vacuum all carpets", completed: false },
      { id: "check2", task: "Clean and sanitize bathrooms", completed: true },
      { id: "check3", task: "Empty trash bins", completed: false },
      { id: "check4", task: "Dust all surfaces", completed: false },
      { id: "check5", task: "Mop hard floors", completed: true },
    ],
  },
  {
    id: "task2",
    title: "Retail Store B",
    status: "Pending",
    checklist: [
      { id: "check6", task: "Clean display windows", completed: false },
      { id: "check7", task: "Sanitize checkout counters", completed: false },
      { id: "check8", task: "Restock cleaning supplies", completed: true },
      { id: "check9", task: "Clean fitting rooms", completed: false },
    ],
  },
];

const schedules = [
  {
    id: "schedule1",
    date: "2023-10-16",
    shift: "Morning Shift",
    time: "6:00 AM - 2:00 PM",
    location: "Office Building A",
  },
  {
    id: "schedule2",
    date: "2023-10-17",
    shift: "Afternoon Shift",
    time: "2:00 PM - 10:00 PM",
    location: "Retail Store B",
  },
];

export type RootStackParamList = {
  Chat: undefined;
  Settings: undefined;
  AskARA: undefined;
};

const AskARAScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState("home");
  const [expandedTask, setExpandedTask] = useState<string | null>(null);
  const navigation = useNavigation();

  const toggleTaskExpansion = (taskId: string) => {
    setExpandedTask(expandedTask === taskId ? null : taskId);
  };

  const renderDashboard = () => (
    <ScrollView style={styles.tabContent}>
      <Card style={styles.jobCard}>
        <Card.Content>
          <View style={styles.cardHeader}>
            <Title style={styles.cardTitle}>{currentJob.title}</Title>
            <Ionicons name="sparkles" size={16} color="#fbbf24" />
          </View>
          
          <View style={styles.progressContainer}>
            <View style={styles.circularProgress}>
              <Text style={styles.progressText}>{currentJob.progress}%</Text>
            </View>
            
            <View style={styles.jobDetails}>
              <Text style={styles.clientText}>Client: {currentJob.client}</Text>
              <Text style={styles.timeText}>Time Remaining: {currentJob.timeRemaining}</Text>
            </View>
          </View>

          <View style={styles.tasksGrid}>
            <View style={styles.taskColumn}>
              <Text style={styles.columnTitle}>Completed Tasks</Text>
              {currentJob.rooms.cleaned.slice(0, 3).map((room, index) => (
                <View key={index} style={styles.taskItem}>
                  <Ionicons name="checkmark-circle" size={14} color="#4ade80" />
                  <Text style={styles.taskText}>{room}</Text>
                </View>
              ))}
            </View>
            
            <View style={styles.taskColumn}>
              <Text style={styles.columnTitle}>Remaining Tasks</Text>
              {currentJob.rooms.remaining.slice(0, 3).map((room, index) => (
                <View key={index} style={styles.taskItem}>
                  <Ionicons name="square-outline" size={14} color="#9ca3af" />
                  <Text style={styles.taskText}>{room}</Text>
                </View>
              ))}
            </View>
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.sectionCard}>
        <Card.Content>
          <Title style={styles.sectionTitle}>TASKS BREAKDOWN</Title>
          {tasks.map((task) => (
            <View key={task.id} style={styles.taskCard}>
              <TouchableOpacity
                style={styles.taskHeader}
                onPress={() => toggleTaskExpansion(task.id)}
              >
                <Text style={styles.taskTitle}>{task.title}</Text>
                <View style={styles.taskStatusContainer}>
                  <Badge
                    style={[
                      styles.taskBadge,
                      task.status === "Completed" && styles.badgeSuccess,
                      task.status === "In Progress" && styles.badgeWarning,
                      task.status === "Pending" && styles.badgeSecondary,
                    ]}
                  >
                    {task.status}
                  </Badge>
                  <Ionicons
                    name={expandedTask === task.id ? "chevron-up" : "chevron-down"}
                    size={16}
                    color="#6b7280"
                  />
                </View>
              </TouchableOpacity>
              
              {expandedTask === task.id && (
                <View style={styles.taskChecklist}>
                  {task.checklist.map((item) => (
                    <View key={item.id} style={styles.checklistItem}>
                      {item.completed ? (
                        <Ionicons name="checkmark-square" size={16} color="#4ade80" />
                      ) : (
                        <Ionicons name="square-outline" size={16} color="#d1d5db" />
                      )}
                      <Text
                        style={[
                          styles.checklistText,
                          item.completed && styles.checklistTextCompleted,
                        ]}
                      >
                        {item.task}
                      </Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          ))}
        </Card.Content>
      </Card>

      <Card style={styles.sectionCard}>
        <Card.Content>
          <Title style={styles.sectionTitle}>UPCOMING SHIFTS</Title>
          {schedules.map((schedule) => (
            <Card key={schedule.id} style={styles.scheduleCard}>
              <Card.Content>
                <Title style={styles.scheduleTitle}>
                  {new Date(schedule.date).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </Title>
                <Text style={styles.scheduleShift}>{schedule.shift}</Text>
                <Text style={styles.scheduleTime}>{schedule.time}</Text>
                <Text style={styles.scheduleLocation}>Location: {schedule.location}</Text>
              </Card.Content>
            </Card>
          ))}
        </Card.Content>
      </Card>
    </ScrollView>
  );

  const renderTasks = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={styles.sectionHeader}>CLEANING TASKS</Text>
      {tasks.map((task) => (
        <Card key={task.id} style={styles.taskCard}>
          <Card.Content>
            <View style={styles.taskHeader}>
              <Text style={styles.taskTitle}>{task.title}</Text>
              <Badge
                style={[
                  styles.taskBadge,
                  task.status === "Completed" && styles.badgeSuccess,
                  task.status === "In Progress" && styles.badgeWarning,
                  task.status === "Pending" && styles.badgeSecondary,
                ]}
              >
                {task.status}
              </Badge>
            </View>
            
            <View style={styles.taskChecklist}>
              {task.checklist.map((item) => (
                <View key={item.id} style={styles.checklistItem}>
                  {item.completed ? (
                    <Ionicons name="checkmark-square" size={16} color="#4ade80" />
                  ) : (
                    <Ionicons name="square-outline" size={16} color="#d1d5db" />
                  )}
                  <Text
                    style={[
                      styles.checklistText,
                      item.completed && styles.checklistTextCompleted,
                    ]}
                  >
                    {item.task}
                  </Text>
                </View>
              ))}
            </View>
          </Card.Content>
        </Card>
      ))}
    </ScrollView>
  );

  const renderSchedule = () => (
    <ScrollView style={styles.tabContent}>
      <Text style={styles.sectionHeader}>UPCOMING SHIFTS</Text>
      <View style={styles.scheduleActions}>
        <PaperButton mode="outlined" onPress={() => {}} style={styles.scheduleButton}>
          <Ionicons name="calendar" size={16} style={styles.buttonIcon} />
          View Calendar
        </PaperButton>
        <PaperButton mode="outlined" onPress={() => {}} style={styles.scheduleButton}>
          <Ionicons name="time" size={16} style={styles.buttonIcon} />
          Request Leave
        </PaperButton>
      </View>
      {schedules.map((schedule) => (
        <Card key={schedule.id} style={styles.scheduleCard}>
          <Card.Content>
            <Title style={styles.scheduleTitle}>
              {new Date(schedule.date).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </Title>
            <Text style={styles.scheduleShift}>{schedule.shift}</Text>
            <Text style={styles.scheduleTime}>{schedule.time}</Text>
            <Text style={styles.scheduleLocation}>Location: {schedule.location}</Text>
          </Card.Content>
        </Card>
      ))}
    </ScrollView>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'home':
        return renderDashboard();
      case 'tasks':
        return renderTasks();
      case 'schedule':
        return renderSchedule();
      default:
        return renderDashboard();
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar backgroundColor={theme.colors.background} barStyle="light-content" />
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>ARA Group CRM</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Settings' as never)}>
            <Ionicons name="settings" size={24} color={theme.colors.primaryText} />
          </TouchableOpacity>
        </View>

        {/* Tab Content */}
        <View style={styles.content}>
          {renderTabContent()}
        </View>

        {/* Bottom Navigation */}
        <View style={styles.bottomNav}>
          <TouchableOpacity
            style={[styles.navButton, activeTab === 'home' && styles.navButtonActive]}
            onPress={() => setActiveTab('home')}
          >
            <Ionicons
              name="home"
              size={24}
              color={activeTab === 'home' ? '#3b82f6' : '#6b7280'}
            />
            <Text style={[styles.navLabel, activeTab === 'home' && styles.navLabelActive]}>
              Home
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, activeTab === 'tasks' && styles.navButtonActive]}
            onPress={() => setActiveTab('tasks')}
          >
            <Ionicons
              name="clipboard-list"
              size={24}
              color={activeTab === 'tasks' ? '#f59e0b' : '#6b7280'}
            />
            <Text style={[styles.navLabel, activeTab === 'tasks' && styles.navLabelActive]}>
              Tasks
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, activeTab === 'schedule' && styles.navButtonActive]}
            onPress={() => setActiveTab('schedule')}
          >
            <Ionicons
              name="calendar"
              size={24}
              color={activeTab === 'schedule' ? '#10b981' : '#6b7280'}
            />
            <Text style={[styles.navLabel, activeTab === 'schedule' && styles.navLabelActive]}>
              Schedule
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    backgroundColor: theme.colors.background,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    color: theme.colors.primaryText,
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
    padding: theme.spacing.md,
  },
  bottomNav: {
    backgroundColor: theme.colors.background,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: theme.spacing.sm,
  },
  navButton: {
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
  },
  navButtonActive: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    borderRadius: 8,
  },
  navLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  navLabelActive: {
    color: '#3b82f6',
    fontWeight: '600',
  },
  jobCard: {
    backgroundColor: '#000',
    marginBottom: theme.spacing.md,
    borderRadius: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  cardTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  circularProgress: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#374151',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  progressText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  jobDetails: {
    flex: 1,
  },
  clientText: {
    color: '#9ca3af',
    fontSize: 12,
    marginBottom: 4,
  },
  timeText: {
    color: '#fff',
    fontSize: 14,
  },
  tasksGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  taskColumn: {
    flex: 1,
  },
  columnTitle: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 8,
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  taskText: {
    color: '#fff',
    fontSize: 11,
    marginLeft: 4,
  },
  sectionCard: {
    marginBottom: theme.spacing.md,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'uppercase',
    marginBottom: theme.spacing.md,
  },
  taskCard: {
    marginBottom: theme.spacing.sm,
    borderRadius: 8,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  taskTitle: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  taskStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  taskBadge: {
    marginRight: 8,
  },
  badgeSuccess: {
    backgroundColor: '#4ade80',
  },
  badgeWarning: {
    backgroundColor: '#fbbf24',
  },
  badgeSecondary: {
    backgroundColor: '#d1d5db',
  },
  taskChecklist: {
    marginTop: 8,
  },
  checklistItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  checklistText: {
    fontSize: 12,
    marginLeft: 8,
    flex: 1,
  },
  checklistTextCompleted: {
    textDecorationLine: 'line-through',
    color: '#9ca3af',
  },
  sectionHeader: {
    fontSize: 14,
    fontWeight: '600',
    textTransform: 'uppercase',
    marginBottom: theme.spacing.md,
    color: theme.colors.secondaryText,
  },
  scheduleActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  scheduleButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  buttonIcon: {
    marginRight: 4,
  },
  scheduleCard: {
    marginBottom: theme.spacing.sm,
    borderRadius: 8,
  },
  scheduleTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  scheduleShift: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 2,
  },
  scheduleTime: {
    fontSize: 11,
    color: '#6b7280',
    marginBottom: 2,
  },
  scheduleLocation: {
    fontSize: 11,
    color: '#6b7280',
  },
});

export default AskARAScreen;