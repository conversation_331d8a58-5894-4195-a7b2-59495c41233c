import React, { useState, useEffect } from 'react';
import { 
  View, 
  StyleSheet, 
  ScrollView, 
  SafeAreaView, 
  StatusBar, 
  TouchableOpacity, 
  Text,
  Dimensions 
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { Card, Title, Paragraph, Avatar, Button as PaperButton, Badge } from 'react-native-paper';
import theme from '../utils/theme';

const { width: screenWidth } = Dimensions.get('window');

// Mock data matching the original ask-ara component
const currentJob = {
  title: "Office Building A - Weekly Cleaning",
  client: "TechCorp Inc.",
  progress: 65,
  tasks: {
    completed: 13,
    inProgress: 5,
    remaining: 7,
  },
  timeRemaining: "2 hours 15 minutes",
  rooms: {
    cleaned: ["Lobby", "Conference Room A", "Offices 101-105", "Restrooms 1F"],
    remaining: ["Offices 106-110", "Break Room", "Restrooms 2F", "Storage Area"],
  },
};

const tasks = [
  {
    id: "task1",
    title: "Office Building A",
    status: "In Progress",
    checklist: [
      { id: "check1", task: "Vacuum all carpets", completed: false },
      { id: "check2", task: "Clean and sanitize bathrooms", completed: true },
      { id: "check3", task: "Empty trash bins", completed: false },
      { id: "check4", task: "Dust all surfaces", completed: false },
      { id: "check5", task: "Mop hard floors", completed: true },
    ],
  },
  {
    id: "task2",
    title: "Retail Store B",
    status: "Pending",
    checklist: [
      { id: "check6", task: "Clean display windows", completed: false },
      { id: "check7", task: "Sanitize checkout counters", completed: false },
      { id: "check8", task: "Restock cleaning supplies", completed: true },
      { id: "check9", task: "Clean fitting rooms", completed: false },
    ],
  },
  {
    id: "task3",
    title: "Medical Clinic C",
    status: "Completed",
    checklist: [
      { id: "check10", task: "Disinfect waiting area", completed: true },
      { id: "check11", task: "Sanitize examination rooms", completed: true },
      { id: "check12", task: "Clean and organize reception", completed: true },
      { id: "check13", task: "Dispose of medical waste", completed: true },
    ],
  },
];

const schedules = [
  {
    id: "schedule1",
    date: "2023-10-16",
    shift: "Morning Shift",
    time: "6:00 AM - 2:00 PM",
    location: "Office Building A",
  },
  {
    id: "schedule2",
    date: "2023-10-17",
    shift: "Afternoon Shift",
    time: "2:00 PM - 10:00 PM",
    location: "Retail Store B",
  },
  {
    id: "schedule3",
    date: "2023-10-18",
    shift: "Morning Shift",
    time: "6:00 AM - 2:00 PM",
    location: "Medical Clinic C",
  },
  {
    id: "schedule4",
    date: "2023-10-19",
    shift: "Night Shift",
    time: "10:00 PM - 6:00 AM",
    location: "Office Building D",
  },
];

const contacts = [
  { name: "John Doe", jobType: "Cleaner", avatar: "JD" },
  { name: "Jane Smith", jobType: "Supervisor", avatar: "JS" },
  { name: "Mike Johnson", jobType: "Manager", avatar: "MJ" },
];

const team = [
  {
    name: "Sarah Manager",
    role: "Supervisor",
    avatar: "https://i.pravatar.cc/150?img=1",
    phone: "+**********",
    email: "<EMAIL>",
  },
  {
    name: "John Cleaner",
    role: "Senior Cleaner",
    avatar: "https://i.pravatar.cc/150?img=2",
    phone: "+**********",
    email: "<EMAIL>",
  },
  {
    name: "Emily Tidy",
    role: "Cleaner",
    avatar: "https://i.pravatar.cc/150?img=3",
    phone: "+**********",
    email: "<EMAIL>",
  },
  {
    name: "Michael Sweep",
    role: "Cleaner",
    avatar: "https://i.pravatar.cc/150?img=4",
    phone: "+**********",
    email: "<EMAIL>",
  },
];

export type RootStackParamList = {
  Chat: undefined;
  Settings: undefined;
  AskARA: undefined;
};

const AskARAScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState("home");
  const [expandedTask, setExpandedTask] = useState<string | null>(null);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const navigation = useNavigation();

  const toggleTaskExpansion = (taskId: string) => {
    setExpandedTask(expandedTask === taskId ? null : taskId);
  };

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'success';
      case 'In Progress':
        return 'warning';
      case 'Pending':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const CircularProgress = ({ value, size = 80 }: { value: number; size?: number }) => (
    <View style={[styles.circularProgress, { width: size, height: size, borderRadius: size / 2 }]} >
      <View style={styles.circularProgressBackground} />
      <View style={[styles.circularProgressFill, { 
        width: size * 0.8, 
        height: size * 0.8, 
        borderRadius: (size * 0.8) / 2,
        backgroundColor: `rgba(74, 222, 128, ${value / 100})`,
      }]} />
      <Text style={[styles.circularProgressText, { fontSize: size * 0.2 }]} >{value}%</Text>
    </View>
  );

  const renderDashboard = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      {/* Current Job Card - Black background with green progress */}
      <Card style={styles.jobCard}>
        <Card.Header>
          <View style={styles.cardHeaderRow}>
            <Title style={styles.jobTitle}>{currentJob.title}</Title>
            <Ionicons name="sparkles" size={16} color="#fbbf24" />
          </View>
        </Card.Header>
        
        <Card.Content>
          <View style={styles.progressSection}>
            <CircularProgress value={currentJob.progress} size={80} />
            
            <View style={styles.jobInfo}>
              <Text style={styles.clientText}>Client: {currentJob.client}</Text>
              <Text style={styles.timeText}>Time Remaining: {currentJob.timeRemaining}</Text>
            </View>
          </View>

          <View style={styles.tasksGrid}>
            <View style={styles.taskColumn}>
              <Text style={styles.columnTitle}>Completed Tasks</Text>
              {currentJob.rooms.cleaned.slice(0, 3).map((room, index) => (
                <View key={index} style={styles.taskItem}>
                  <Ionicons name="checkmark-circle" size={14} color="#4ade80" />
                  <Text style={styles.taskText}>{room}</Text>
                </View>
              ))}
            </View>
            
            <View style={styles.taskColumn}>
              <Text style={styles.columnTitle}>Remaining Tasks</Text>
              {currentJob.rooms.remaining.slice(0, 3).map((room, index) => (
                <View key={index} style={styles.taskItem}>
                  <Ionicons name="square-outline" size={14} color="#9ca3af" />
                  <Text style={styles.taskText}>{room}</Text>
                </View>
              ))}
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Tasks Breakdown */}
      <Card style={styles.sectionCard}>
        <Card.Header>
          <Title style={styles.sectionTitle}>TASKS BREAKDOWN</Title>
        </Card.Header>
        
        <Card.Content>
          {tasks.map((task) => (
            <View key={task.id} style={styles.taskCard}>
              <TouchableOpacity
                style={styles.taskHeader}
                onPress={() => toggleTaskExpansion(task.id)}
              >
                <Text style={styles.taskTitle}>{task.title}</Text>
                <View style={styles.taskStatusContainer}>
                  <Badge
                    style={[
                      styles.taskBadge,
                      task.status === "Completed" && styles.badgeSuccess,
                      task.status === "In Progress" && styles.badgeWarning,
                      task.status === "Pending" && styles.badgeSecondary,
                    ]}
                  >
                    {task.status}
                  </Badge>
                  <Ionicons
                    name={expandedTask === task.id ? "chevron-up" : "chevron-down"}
                    size={16}
                    color="#6b7280"
                  />
                </View>
              </TouchableOpacity>
              
              {expandedTask === task.id && (
                <View style={styles.taskChecklist}>
                  {task.checklist.map((item) => (
                    <View key={item.id} style={styles.checklistItem}>
                      {item.completed ? (
                        <Ionicons name="checkmark-circle" size={14} color="#4ade80" />
                      ) : (
                        <Ionicons name="square-outline" size={14} color="#d1d5db" />
                      )}
                      <Text
                        style={[
                          styles.checklistText,
                          item.completed && styles.checklistTextCompleted,
                        ]}
                      >
                        {item.task}
                      </Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          ))}
        </Card.Content>
      </Card>

      {/* Upcoming Shifts */}
      <Card style={styles.sectionCard}>
        <Card.Header>
          <Title style={styles.sectionTitle}>UPCOMING SHIFTS</Title>
        </Card.Header>
        
        <Card.Content>
          {schedules.slice(0, 3).map((schedule) => (
            <Card key={schedule.id} style={styles.scheduleCard}>
              <Card.Header spacing="compact">
                <CardTitle size="sm" weight="semibold">
                  {new Date(schedule.date).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </CardTitle>
              </Card.Header>
              
              <Card.Content>
                <Text style={styles.scheduleShift}>{schedule.shift}</Text>
                <Text style={styles.scheduleTime}>{schedule.time}</Text>
                <Text style={styles.scheduleLocation}>Location: {schedule.location}</Text>
              </Card.Content>
            </Card>
          ))}
        </Card.Content>
      </Card>

      {/* ARA GROUP CONTACTS */}
      <Card style={styles.sectionCard}>
        <Card.Header>
          <Title style={styles.sectionTitle}>ARA GROUP CONTACTS</Title>
        </Card.Header>
        
        <Card.Content>
          {contacts.map((contact, index) => (
            <View key={index} style={styles.contactItem}>
              <View style={styles.contactAvatar}>
                <Text style={styles.contactAvatarText}>{contact.avatar}</Text>
              </View>
              
              <View style={styles.contactInfo}>
                <Text style={styles.contactName}>{contact.name}</Text>
                <Text style={styles.contactRole}>{contact.jobType}</Text>
              </View>
            </View>
          ))}
        </Card.Content>
      </Card>
    </ScrollView>
  );

  const renderTasks = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <Text style={styles.sectionHeader}>CLEANING TASKS</Text>
      {tasks.map((task) => (
        <Card key={task.id} style={styles.taskCard}>
          <Card.Header spacing="compact">
            <View style={styles.taskHeaderRow}>
              <CardTitle size="sm" weight="semibold" style={styles.taskCardTitle}>{task.title}</CardTitle>
              <Badge
                style={[
                  styles.taskBadge,
                  task.status === "Completed" && styles.badgeSuccess,
                  task.status === "In Progress" && styles.badgeWarning,
                  task.status === "Pending" && styles.badgeSecondary,
                ]}
              >
                {task.status}
              </Badge>
            </View>
          </Card.Header>
          
          <Card.Content>
            <View style={styles.taskChecklist}>
              {task.checklist.map((item) => (
                <View key={item.id} style={styles.checklistItem}>
                  {item.completed ? (
                    <Ionicons name="checkmark-circle" size={14} color="#4ade80" />
                  ) : (
                    <Ionicons name="square-outline" size={14} color="#d1d5db" />
                  )}
                  <Text
                    style={[
                      styles.checklistText,
                      item.completed && styles.checklistTextCompleted,
                    ]}
                  >
                    {item.task}
                  </Text>
                </View>
              ))}
            </View>
          </Card.Content>
        </Card>
      ))}
    </ScrollView>
  );

  const renderSchedule = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <Text style={styles.sectionHeader}>UPCOMING SHIFTS</Text>
      
      <View style={styles.scheduleActions}>
        <PaperButton mode="outlined" onPress={() => {}} style={styles.scheduleButton}>
          <Ionicons name="calendar" size={16} style={styles.buttonIcon} />
          View Calendar
        </PaperButton>
        <PaperButton mode="outlined" onPress={() => {}} style={styles.scheduleButton}>
          <Ionicons name="time" size={16} style={styles.buttonIcon} />
          Request Leave
        </PaperButton>
      </View>
      
      {schedules.map((schedule) => (
        <Card key={schedule.id} style={styles.scheduleCard}>
          <Card.Header spacing="compact">
            <CardTitle size="sm" weight="semibold">
              {new Date(schedule.date).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </CardTitle>
          </Card.Header>
          
          <Card.Content>
            <Text style={styles.scheduleShift}>{schedule.shift}</Text>
            <Text style={styles.scheduleTime}>{schedule.time}</Text>
            <Text style={styles.scheduleLocation}>Location: {schedule.location}</Text>
          </Card.Content>
        </Card>
      ))}
    </ScrollView>
  );

  const renderTeam = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <Text style={styles.sectionHeader}>CLEANING TEAM</Text>
      {team.map((member, index) => (
        <Card key={index} style={styles.teamCard}>
          <Card.Content>
            <View style={styles.teamMemberRow}>
              <View style={styles.teamAvatar}>
                <Text style={styles.teamAvatarText}>
                  {member.name.split(' ').map((n) => n[0]).join('')}
                </Text>
              </View>
              
              <View style={styles.teamInfo}>
                <Text style={styles.teamMemberName}>{member.name}</Text>
                <Text style={styles.teamMemberRole}>{member.role}</Text>
              </View>
              
              <View style={styles.teamActions}>
                <PaperButton mode="outlined" onPress={() => {}} style={styles.teamActionButton}>
                  <Ionicons name="call" size={16} color={theme.colors.accent} />
                </PaperButton>
                <PaperButton mode="outlined" onPress={() => {}} style={styles.teamActionButton}>
                  <Ionicons name="chatbubbles" size={16} color={theme.colors.accent} />
                </PaperButton>
              </View>
            </View>
          </Card.Content>
        </Card>
      ))}
    </ScrollView>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'home':
        return renderDashboard();
      case 'tasks':
        return renderTasks();
      case 'schedule':
        return renderSchedule();
      case 'team':
        return renderTeam();
      default:
        return renderDashboard();
    }
  };

  return (
    <SafeAreaView style={[styles.safeArea, isDarkMode && styles.safeAreaDark]}>
      <StatusBar 
        backgroundColor={isDarkMode ? theme.colors.dark.background : theme.colors.background} 
        barStyle={isDarkMode ? "light-content" : "dark-content"} 
      />
      
      <View style={[styles.container, isDarkMode && styles.containerDark]}>
        {/* Header */}
        <View style={[styles.header, isDarkMode && styles.headerDark]}>
          <Text style={[styles.headerTitle, isDarkMode && styles.headerTitleDark]}>ARA Group CRM</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity onPress={toggleTheme} style={styles.headerButton}>
              <Ionicons 
                name={isDarkMode ? "sunny" : "moon"} 
                size={20} 
                color={isDarkMode ? theme.colors.dark.foreground : theme.colors.foreground} 
              />
            </TouchableOpacity>
            
            <TouchableOpacity 
              onPress={() => navigation.navigate('Settings' as never)} 
              style={styles.headerButton}
            >
              <Ionicons 
                name="settings" 
                size={20} 
                color={isDarkMode ? theme.colors.dark.foreground : theme.colors.foreground} 
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Tab Content */}
        <View style={styles.content}>
          {renderTabContent()}
        </View>

        {/* Bottom Navigation */}
        <View style={[styles.bottomNav, isDarkMode && styles.bottomNavDark]}>
          <TouchableOpacity
            style={[styles.navButton, activeTab === 'home' && styles.navButtonActive]}
            onPress={() => setActiveTab('home')}
          >
            <Ionicons
              name="home"
              size={24}
              color={activeTab === 'home' ? theme.colors.blue[500] : theme.colors.gray[500]}
            />
            <Text style={[styles.navLabel, activeTab === 'home' && styles.navLabelActive]}>
              Home
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, activeTab === 'tasks' && styles.navButtonActive]}
            onPress={() => setActiveTab('tasks')}
          >
            <Ionicons
              name="clipboard"
              size={24}
              color={activeTab === 'tasks' ? theme.colors.yellow[500] : theme.colors.gray[500]}
            />
            <Text style={[styles.navLabel, activeTab === 'tasks' && styles.navLabelActive]}>
              Tasks
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, activeTab === 'schedule' && styles.navButtonActive]}
            onPress={() => setActiveTab('schedule')}
          >
            <Ionicons
              name="calendar"
              size={24}
              color={activeTab === 'schedule' ? theme.colors.green[500] : theme.colors.gray[500]}
            />
            <Text style={[styles.navLabel, activeTab === 'schedule' && styles.navLabelActive]}>
              Schedule
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, activeTab === 'team' && styles.navButtonActive]}
            onPress={() => setActiveTab('team')}
          >
            <Ionicons
              name="people"
              size={24}
              color={activeTab === 'team' ? theme.colors.purple[500] : theme.colors.gray[500]}
            />
            <Text style={[styles.navLabel, activeTab === 'team' && styles.navLabelActive]}>
              Team
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  // Layout
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  safeAreaDark: {
    backgroundColor: theme.colors.dark.background,
  },
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  containerDark: {
    backgroundColor: theme.colors.dark.background,
  },
  content: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
    padding: theme.spacing.md,
  },

  // Header
  header: {
    backgroundColor: theme.colors.background,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerDark: {
    backgroundColor: theme.colors.dark.background,
    borderBottomColor: theme.colors.dark.border,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primaryText,
    letterSpacing: -0.025,
  },
  headerTitleDark: {
    color: theme.colors.dark.foreground,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  headerButton: {
    padding: theme.spacing.sm,
    borderRadius: theme.spacing.sm,
  },

  // Navigation
  bottomNav: {
    backgroundColor: theme.colors.background,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.sm,
  },
  bottomNavDark: {
    backgroundColor: theme.colors.dark.background,
    borderTopColor: theme.colors.dark.border,
  },
  navButton: {
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.spacing.lg,
    minWidth: 60,
  },
  navButtonActive: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
  },
  navLabel: {
    fontSize: 12,
    color: theme.colors.gray[500],
    marginTop: 2,
    fontWeight: '500',
  },
  navLabelActive: {
    color: theme.colors.blue[500],
    fontWeight: '600',
  },

  // Dashboard
  jobCard: {
    backgroundColor: '#000',
    marginBottom: theme.spacing.md,
    borderRadius: theme.spacing.lg,
    borderWidth: 1,
    borderColor: '#333',
  },
  cardHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  jobTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  progressSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  circularProgress: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
    position: 'relative',
  },
  circularProgressBackground: {
    position: 'absolute',
    backgroundColor: '#374151',
    borderRadius: 9999,
  },
  circularProgressFill: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  circularProgressText: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  jobInfo: {
    flex: 1,
  },
  clientText: {
    color: '#9ca3af',
    fontSize: 12,
    marginBottom: 4,
  },
  timeText: {
    color: '#fff',
    fontSize: 14,
  },
  tasksGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: theme.spacing.md,
  },
  taskColumn: {
    flex: 1,
  },
  columnTitle: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
    marginBottom: 8,
    textTransform: 'uppercase',
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 4,
  },
  taskText: {
    color: '#fff',
    fontSize: 10,
  },

  // Section Cards
  sectionCard: {
    marginBottom: theme.spacing.md,
    borderRadius: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    color: theme.colors.gray[500],
  },

  // Task Cards
  taskCard: {
    marginBottom: 12,
    borderRadius: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  taskHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  taskCardTitle: {
    flex: 1,
    fontSize: 14,
  },
  taskTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primaryText,
    flex: 1,
  },
  taskStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  taskBadge: {
    marginRight: 8,
  },
  badgeSuccess: {
    backgroundColor: '#4ade80',
  },
  badgeWarning: {
    backgroundColor: '#fbbf24',
  },
  badgeSecondary: {
    backgroundColor: '#d1d5db',
  },
  taskChecklist: {
    paddingTop: 12,
  },
  checklistItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  checklistText: {
    fontSize: 12,
    color: theme.colors.primaryText,
    flex: 1,
  },
  checklistTextCompleted: {
    textDecorationLine: 'line-through',
    color: theme.colors.gray[500],
  },

  // Schedule
  sectionHeader: {
    fontSize: 14,
    fontWeight: '600',
    textTransform: 'uppercase',
    marginBottom: 16,
    color: theme.colors.gray[500],
    letterSpacing: 0.5,
  },
  scheduleActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    gap: 8,
  },
  scheduleButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  buttonIcon: {
    marginRight: 4,
  },
  scheduleCard: {
    marginBottom: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  scheduleShift: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  scheduleTime: {
    fontSize: 11,
    color: theme.colors.gray[500],
    marginBottom: 2,
  },
  scheduleLocation: {
    fontSize: 11,
    color: theme.colors.gray[500],
  },

  // Team
  teamCard: {
    marginBottom: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  teamMemberRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  teamAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.accent,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  teamAvatarText: {
    color: theme.colors.primaryText,
    fontSize: 12,
    fontWeight: '600',
  },
  teamInfo: {
    flex: 1,
  },
  teamMemberName: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primaryText,
  },
  teamMemberRole: {
    fontSize: 11,
    color: theme.colors.gray[500],
  },
  teamActions: {
    flexDirection: 'row',
    gap: 8,
  },
  teamActionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    padding: 0,
  },

  // Contacts
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  contactAvatar: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: theme.colors.accent,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  contactAvatarText: {
    color: theme.colors.primaryText,
    fontSize: 10,
    fontWeight: '600',
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.primaryText,
  },
  contactRole: {
    fontSize: 10,
    color: theme.colors.gray[500],
  },
});

export default AskARAScreen;