// Design system based on the original ask-ara implementation
// This recreates the Radix UI + Tailwind CSS design system in React Native

import { StyleSheet } from 'react-native';

// Color system based on CSS custom properties from globals.css
export const colors = {
  // CSS variable equivalents
  background: 'hsl(0, 0%, 100%)',                    // --background
  foreground: 'hsl(240, 10%, 3.9%)',               // --foreground
  card: 'hsl(0, 0%, 100%)',                        // --card
  cardForeground: 'hsl(240, 10%, 3.9%)',            // --card-foreground
  popover: 'hsl(0, 0%, 100%)',                     // --popover
  popoverForeground: 'hsl(240, 10%, 3.9%)',        // --popover-foreground
  
  // Primary color scale
  primary: {
    DEFAULT: 'hsl(240, 5.9%, 10%)',                 // --primary
    foreground: 'hsl(0, 0%, 98%)',                // --primary-foreground
    50: 'hsl(240, 9%, 98%)',                       // --primary-50
    100: 'hsl(240, 9%, 95%)',                      // --primary-100
    200: 'hsl(240, 6%, 90%)',                       // --primary-200
    300: 'hsl(240, 5%, 84%)',                      // --primary-300
    400: 'hsl(240, 5%, 65%)',                      // --primary-400
    500: 'hsl(240, 4%, 46%)',                      // --primary-500
    600: 'hsl(240, 5%, 34%)',                      // --primary-600
    700: 'hsl(240, 5%, 26%)',                      // --primary-700
    800: 'hsl(240, 4%, 16%)',                      // --primary-800
    900: 'hsl(240, 6%, 10%)',                      // --primary-900
    950: 'hsl(240, 7%, 8%)',                      // --primary-950
  },
  
  secondary: {
    DEFAULT: 'hsl(240, 4.8%, 95.9%)',               // --secondary
    foreground: 'hsl(240, 5.9%, 10%)',            // --secondary-foreground
  },
  
  muted: {
    DEFAULT: 'hsl(240, 4.8%, 95.9%)',              // --muted
    foreground: 'hsl(240, 3.8%, 46.1%)',          // --muted-foreground
  },
  
  accent: {
    DEFAULT: 'hsl(240, 4.8%, 95.9%)',              // --accent
    foreground: 'hsl(240, 5.9%, 10%)',            // --accent-foreground
  },
  
  destructive: {
    DEFAULT: 'hsl(0, 84.2%, 60.2%)',              // --destructive
    foreground: 'hsl(0, 0%, 98%)',                // --destructive-foreground
  },
  
  border: 'hsl(240, 5.9%, 90%)',                   // --border
  input: 'hsl(240, 5.9%, 90%)',                  // --input
  ring: 'hsl(240, 5.9%, 10%)',                    // --ring
  
  // Chart colors
  chart: {
    1: 'hsl(12, 76%, 61%)',                        // --chart-1
    2: 'hsl(173, 58%, 39%)',                       // --chart-2
    3: 'hsl(197, 37%, 24%)',                       // --chart-3
    4: 'hsl(43, 74%, 66%)',                        // --chart-4
    5: 'hsl(27, 87%, 67%)',                        // --chart-5
  },
  
  // Dark mode colors
  dark: {
    background: 'hsl(240, 10%, 3.9%)',              // --background (dark)
    foreground: 'hsl(0, 0%, 98%)',                  // --foreground (dark)
    card: 'hsl(240, 10%, 3.9%)',                    // --card (dark)
    cardForeground: 'hsl(0, 0%, 98%)',            // --card-foreground (dark)
    popover: 'hsl(240, 10%, 3.9%)',                // --popover (dark)
    popoverForeground: 'hsl(0, 0%, 98%)',         // --popover-foreground (dark)
    
    primary: {
      DEFAULT: 'hsl(0, 0%, 98%)',                   // --primary (dark)
      foreground: 'hsl(240, 5.9%, 10%)',          // --primary-foreground (dark)
      50: 'hsl(240, 6%, 10%)',                      // --primary-50 (dark)
      100: 'hsl(240, 5%, 15%)',                     // --primary-100 (dark)
      200: 'hsl(240, 5%, 21%)',                     // --primary-200 (dark)
      300: 'hsl(240, 4%, 28%)',                     // --primary-300 (dark)
      400: 'hsl(240, 5%, 44%)',                     // --primary-400 (dark)
      500: 'hsl(240, 4%, 58%)',                     // --primary-500 (dark)
      600: 'hsl(240, 5%, 65%)',                     // --primary-600 (dark)
      700: 'hsl(240, 5%, 84%)',                     // --primary-700 (dark)
      800: 'hsl(240, 6%, 90%)',                     // --primary-800 (dark)
      900: 'hsl(240, 9%, 95%)',                     // --primary-900 (dark)
      950: 'hsl(240, 9%, 98%)',                     // --primary-950 (dark)
    },
    
    secondary: {
      DEFAULT: 'hsl(240, 3.7%, 15.9%)',            // --secondary (dark)
      foreground: 'hsl(0, 0%, 98%)',               // --secondary-foreground (dark)
    },
    
    muted: {
      DEFAULT: 'hsl(240, 3.7%, 15.9%)',            // --muted (dark)
      foreground: 'hsl(240, 5%, 64.9%)',        // --muted-foreground (dark)
    },
    
    accent: {
      DEFAULT: 'hsl(240, 3.7%, 15.9%)',            // --accent (dark)
      foreground: 'hsl(0, 0%, 98%)',              // --accent-foreground (dark)
    },
    
    destructive: {
      DEFAULT: 'hsl(0, 62.8%, 30.6%)',              // --destructive (dark)
      foreground: 'hsl(0, 0%, 98%)',              // --destructive-foreground (dark)
    },
    
    border: 'hsl(240, 3.7%, 15.9%)',             // --border (dark)
    input: 'hsl(240, 3.7%, 15.9%)',                // --input (dark)
    ring: 'hsl(240, 4.9%, 83.9%)',                 // --ring (dark)
    
    chart: {
      1: 'hsl(220, 70%, 50%)',                      // --chart-1 (dark)
      2: 'hsl(160, 60%, 45%)',                      // --chart-2 (dark)
      3: 'hsl(30, 80%, 55%)',                       // --chart-3 (dark)
      4: 'hsl(280, 65%, 60%)',                      // --chart-4 (dark)
      5: 'hsl(340, 75%, 55%)',                      // --chart-5 (dark)
    },
  },
  
  // Additional colors from the original design
  blue: {
    500: '#3b82f6',
    600: '#2563eb',
  },
  green: {
    500: '#4ade80',
    600: '#16a34a',
  },
  yellow: {
    500: '#fbbf24',
    600: '#f59e0b',
  },
  purple: {
    500: '#a855f7',
    600: '#9333ea',
  },
  gray: {
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
};

// Border radius system
export const radius = {
  sm: 4,    // calc(var(--radius) - 4px)
  md: 6,    // calc(var(--radius) - 2px)
  lg: 8,    // var(--radius)
  xl: 12,   // calc(var(--radius) + 4px)
  full: 9999,
};

// Spacing system
export const spacing = {
  px: 1,
  0: 0,
  0.5: 2,
  1: 4,
  1.5: 6,
  2: 8,
  2.5: 10,
  3: 12,
  3.5: 14,
  4: 16,
  5: 20,
  6: 24,
  7: 28,
  8: 32,
  9: 36,
  10: 40,
  11: 44,
  12: 48,
  14: 56,
  16: 64,
  20: 80,
  24: 96,
  28: 112,
  32: 128,
  36: 144,
  40: 160,
  44: 176,
  48: 192,
  52: 208,
  56: 224,
  60: 240,
  64: 256,
  72: 288,
  80: 320,
  96: 384,
};

// Typography system
export const typography = {
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
};

// Shadow system
export const shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.25,
    shadowRadius: 25,
    elevation: 12,
  },
};

// Design tokens for consistent styling
export const designTokens = {
  colors,
  radius,
  spacing,
  typography,
  shadows,
};

// Utility function similar to cn() from the original
export function cn(...classes: (string | undefined | null | false)[]) {
  return classes.filter(Boolean).join(' ');
}